<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogShow" width="40%" class="dialog-container">
      <el-form
        :model="formData"
        :rules="formDataRules"
        label-width="100px"
        ref="formDataRef"
        :disabled="false"
        class="demo-ruleForm"
      >
        <el-form-item v-if="type === '3'" label="截止时间" prop="endTime">
          <el-date-picker
            style="width: 100%; margin-right: 20px"
            v-model="formData.endTime"
            type="datetime"
            placeholder="选择日期时间"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item v-if="type === '2'" label="作废原因" prop="cancelResultType">
          <el-select v-model="formData.cancelResultType" style="width: 100%; margin-right: 20px">
            <el-option
              v-for="item in cancelResultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="type === '1'" label="审核结果" prop="auditResultType">
          <el-radio-group v-model="formData.auditResultType">
            <el-radio :label="1">同意</el-radio>
            <el-radio :label="0">返回修改</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 通用备注 -->
        <el-form-item :label="remarkTitle" prop="remark">
          <el-input
            type="textarea"
            :rows="6"
            style="width: 100%; margin-right: 20px"
            clearable
            v-model="formData.remark"
            :placeholder="`请输入${remarkTitle}`"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button style="padding: 0px 20px" @click="dialogShow = false"
          >取 消</el-button
        >
        <el-button style="padding: 0px 20px" type="primary" @click="handleSave"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
    props: {
        dialogShow: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        remarkTitle: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: '', //1:截止时间弹框；2:作废原因弹框；3：审核弹框
        },
    },
    watch: {
    // 监听type变化，动态重置表单数据
        type (val) {
            this.resetFormByType(val)
        },
    },
    data () {
        return {
            formData: {},
            formDataRules: {
                auditResultType: [
                    { required: true, message: '请选择审核结果', trigger: 'blur' },
                ],
                remark: [
                    {
                        required: true,
                        message: `请输入${this.remarkTitle}`,
                        trigger: 'blur',
                    },
                ],
                cancelResultType: [
                    { required: true, message: '请选择作废原因', trigger: 'blur' },
                ],
            },
            pickerOptions: {
                // 禁止选择过去的时间
                disabledDate (time) {
                    return time.getTime() < Date.now() - 8.64e7
                },
            },
            cancelResultOptions: [{
                value: 1,
                label: '数据错误',
            }, {
                value: 2,
                label: '不符合业务要求',
            }, {
                value: 3,
                label: '不在此功能录入',
            }, {
                value: 4,
                label: '其他',
            }]
        }
    },
    created () {
    // 初始化表单
        //this.resetFormByType(this.type)
    },
    methods: {
        init (state) {
            this.dialogShow = true
            if(this.$refs.formDataRef) {
                this.$refs.formDataRef.clearValidate()
            }
            this.type = state
            if (state === '1') {
                this.remarkTitle = '审核意见'
                this.title = '审核'
            } else if (state === '2') {
                this.remarkTitle = '作废说明'
                this.title = '作废'
            }
            this.resetFormByType(state)
        },
        resetFormByType (type) {
            switch (type) {
            case '1':
                this.formData = {
                    auditResultType: 1,
                    remark: '',
                }
                this.formDataRules.remark[0].message = `请输入${this.remarkTitle}`
                /*this.formDataRules = {
                    auditResultType: [
                        { required: true, message: '请选择审核结果', trigger: 'blur' },
                    ],
                    remark: [
                        {
                            required: true,
                            message: `请输入${this.remarkTitle}`,
                            trigger: 'blur',
                        },
                    ],
                }*/
                break
            case '2':
                // 类型2：作废原因场景
                this.formData = {
                    cancelResultType: 1,
                    remark: '',
                }
                this.formDataRules.remark[0].message = `请输入${this.remarkTitle}`
                /*this.formDataRules = {
                    cancelResultType: [
                        { required: true, message: '请选择作废原因', trigger: 'blur' },
                    ],
                    remark: [
                        {
                            required: true,
                            message: `请输入${this.remarkTitle}`,
                            trigger: 'blur',
                        },
                    ],
                }*/
                break
            case '3':
                // 类型3：审核
                this.formData = {
                    auditResultType: 1,
                    remark: '',
                }
                this.formDataRules = {
                    auditResultType: [
                        { required: true, message: '请选择审核结果', trigger: 'blur' },
                    ],
                    remark: [
                        {
                            required: true,
                            message: `请输入${this.remarkTitle}`,
                            trigger: 'blur',
                        },
                    ],
                }
                break
            default:
                // 默认场景
                this.formData = {
                    remark: '',
                }
                this.formDataRules = {
                    remark: [
                        {
                            required: true,
                            message: `请输入${this.remarkTitle}`,
                            trigger: 'blur',
                        },
                    ],
                }
            }
        },

        // 保存表单
        handleSave () {
            this.$refs.formDataRef.validate(valid => {
                if (valid) {
                    this.$emit('submitForm', this.formData)
                    this.dialogShow = false
                }
            })
        },
    },
}
</script>

<style lang="scss" scoped>
/deep/ .dialog-container {
   .el-dialog {
     .el-dialog__body {
       height: unset;
     }
  }
}
</style>
